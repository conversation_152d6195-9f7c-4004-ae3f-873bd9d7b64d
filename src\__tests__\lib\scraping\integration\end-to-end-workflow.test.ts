/**
 * Integration Tests for End-to-End Scraping Workflow
 * Tests complete workflow including job processing, data transformation, and database storage
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { contentProcessor } from '@/lib/scraping/content-processor';
import { EnhancedScrapeRequest, EnhancedScrapeResult } from '@/lib/scraping/types';

// Mock Supabase client
const mockSupabase = {
  from: jest.fn().mockReturnThis(),
  insert: jest.fn().mockReturnThis(),
  select: jest.fn().mockReturnThis(),
  eq: jest.fn().mockReturnThis(),
  single: jest.fn()
};

jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => mockSupabase)
}));

// Mock the scrape-do client
const mockScrapeDoClient = {
  scrapePage: jest.fn(),
  getUsageStatistics: jest.fn()
};

jest.mock('@/lib/scraping/scrape-do-client', () => ({
  scrapeDoClient: mockScrapeDoClient
}));

// Mock the cost optimizer
const mockCostOptimizer = {
  scrapeWithMaxCostOptimization: jest.fn()
};

jest.mock('@/lib/scraping/cost-optimizer', () => ({
  costOptimizer: mockCostOptimizer
}));

// Mock the media extractor
const mockMediaExtractor = {
  collectImagesWithPriority: jest.fn()
};

jest.mock('@/lib/scraping/media-extractor', () => ({
  mediaExtractor: mockMediaExtractor
}));

// Mock the multi-page scraper
const mockMultiPageScraper = {
  discoverAndPlanScraping: jest.fn(),
  executeMultiPageScraping: jest.fn()
};

jest.mock('@/lib/scraping/multi-page-scraper', () => ({
  multiPageScraper: mockMultiPageScraper
}));

// Mock file system operations
const mockFs = {
  writeFile: jest.fn(),
  mkdir: jest.fn(),
  access: jest.fn()
};

jest.mock('fs/promises', () => mockFs);

describe('End-to-End Scraping Workflow Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default mocks
    mockScrapeDoClient.getUsageStatistics.mockResolvedValue({
      remainingMonthlyRequests: 1000,
      usedMonthlyRequests: 500,
      isActive: true
    });

    mockSupabase.single.mockResolvedValue({
      data: null,
      error: null
    });
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('Complete Workflow Integration', () => {
    it('should execute complete scraping workflow successfully', async () => {
      // Setup mock responses
      const mockMainContent = `
        <html>
          <head>
            <title>Test Product</title>
            <meta property="og:title" content="Test Product">
            <meta property="og:description" content="A great test product">
            <link rel="icon" href="/favicon.ico">
          </head>
          <body>
            <h1>Test Product</h1>
            <p>This is a comprehensive product description.</p>
            <nav>
              <a href="/pricing">Pricing</a>
              <a href="/features">Features</a>
            </nav>
          </body>
        </html>
      `;

      mockCostOptimizer.scrapeWithMaxCostOptimization.mockResolvedValue({
        success: true,
        content: mockMainContent,
        url: 'https://example.com',
        metadata: {
          creditsUsed: 1,
          requestType: 'Datacenter'
        }
      });

      mockMediaExtractor.collectImagesWithPriority.mockResolvedValue({
        favicon: ['https://example.com/favicon.ico'],
        ogImages: [{
          type: 'og:image',
          url: 'https://example.com/og-image.jpg',
          priority: 1
        }],
        screenshot: null
      });

      mockMultiPageScraper.discoverAndPlanScraping.mockResolvedValue({
        scrapeNow: [{
          pageType: 'pricing',
          url: 'https://example.com/pricing',
          confidence: 0.9,
          foundMethod: 'link',
          priority: 'high',
          estimatedCredits: 1
        }],
        queueForLater: [],
        skipPages: [],
        reason: 'Sufficient credits available'
      });

      mockMultiPageScraper.executeMultiPageScraping.mockResolvedValue([{
        success: true,
        content: 'Pricing: $29/month',
        url: 'https://example.com/pricing',
        metadata: {
          creditsUsed: 1,
          pageType: 'pricing'
        }
      }]);

      // Execute the workflow
      const request: EnhancedScrapeRequest = {
        url: 'https://example.com',
        options: {
          outputFormat: 'markdown',
          timeout: 30000
        },
        costOptimization: true,
        mediaCollection: true,
        persistentStorage: true
      };

      const result = await contentProcessor.processEnhancedScrape(request);

      // Verify the complete workflow
      expect(result.success).toBe(true);
      expect(result.content).toContain('Test Product');
      expect(result.mediaAssets).toBeDefined();
      expect(result.mediaAssets?.favicon).toEqual(['https://example.com/favicon.ico']);
      expect(result.additionalPages).toHaveLength(1);
      expect(result.costAnalysis).toBeDefined();
      expect(result.costAnalysis?.creditsUsed).toBe(2); // Main page + pricing page
    });

    it('should handle workflow failures gracefully', async () => {
      // Mock main scraping failure
      mockCostOptimizer.scrapeWithMaxCostOptimization.mockResolvedValue({
        success: false,
        error: 'Scraping failed',
        content: '',
        url: 'https://example.com'
      });

      const request: EnhancedScrapeRequest = {
        url: 'https://example.com',
        options: {
          outputFormat: 'markdown'
        }
      };

      const result = await contentProcessor.processEnhancedScrape(request);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Scraping failed');
    });

    it('should handle partial workflow success', async () => {
      // Main scraping succeeds, but media collection fails
      mockCostOptimizer.scrapeWithMaxCostOptimization.mockResolvedValue({
        success: true,
        content: 'Main content',
        url: 'https://example.com',
        metadata: { creditsUsed: 1 }
      });

      mockMediaExtractor.collectImagesWithPriority.mockRejectedValue(
        new Error('Media collection failed')
      );

      mockMultiPageScraper.discoverAndPlanScraping.mockResolvedValue({
        scrapeNow: [],
        queueForLater: [],
        skipPages: [],
        reason: 'No additional pages found'
      });

      const request: EnhancedScrapeRequest = {
        url: 'https://example.com',
        options: { outputFormat: 'markdown' },
        mediaCollection: true
      };

      const result = await contentProcessor.processEnhancedScrape(request);

      expect(result.success).toBe(true);
      expect(result.content).toBe('Main content');
      expect(result.mediaAssets).toBeUndefined(); // Media collection failed
    });
  });

  describe('Data Transformation Pipeline', () => {
    it('should transform scraped data correctly', async () => {
      const rawHtmlContent = `
        <html>
          <head>
            <title>Product Title</title>
            <meta name="description" content="Product description">
          </head>
          <body>
            <h1>Product Title</h1>
            <p>Product description with <strong>formatting</strong>.</p>
            <ul>
              <li>Feature 1</li>
              <li>Feature 2</li>
            </ul>
          </body>
        </html>
      `;

      mockCostOptimizer.scrapeWithMaxCostOptimization.mockResolvedValue({
        success: true,
        content: rawHtmlContent,
        url: 'https://example.com',
        metadata: { creditsUsed: 1 }
      });

      mockMediaExtractor.collectImagesWithPriority.mockResolvedValue({
        favicon: null,
        ogImages: [],
        screenshot: null
      });

      mockMultiPageScraper.discoverAndPlanScraping.mockResolvedValue({
        scrapeNow: [],
        queueForLater: [],
        skipPages: [],
        reason: 'No additional pages'
      });

      const request: EnhancedScrapeRequest = {
        url: 'https://example.com',
        options: { outputFormat: 'markdown' }
      };

      const result = await contentProcessor.processEnhancedScrape(request);

      expect(result.success).toBe(true);
      expect(result.content).toContain('Product Title');
      expect(result.content).toContain('Product description');
    });

    it('should extract structured data correctly', async () => {
      const structuredContent = `
        <html>
          <body>
            <div class="pricing">
              <h2>Pricing Plans</h2>
              <div class="plan">
                <h3>Basic</h3>
                <p>$10/month</p>
              </div>
            </div>
            <div class="faq">
              <h2>FAQ</h2>
              <div class="question">What is this?</div>
              <div class="answer">This is a test product.</div>
            </div>
          </body>
        </html>
      `;

      mockCostOptimizer.scrapeWithMaxCostOptimization.mockResolvedValue({
        success: true,
        content: structuredContent,
        url: 'https://example.com',
        metadata: { creditsUsed: 1 }
      });

      mockMediaExtractor.collectImagesWithPriority.mockResolvedValue({
        favicon: null,
        ogImages: [],
        screenshot: null
      });

      mockMultiPageScraper.discoverAndPlanScraping.mockResolvedValue({
        scrapeNow: [],
        queueForLater: [],
        skipPages: [],
        reason: 'Content found in main page'
      });

      const request: EnhancedScrapeRequest = {
        url: 'https://example.com',
        options: { outputFormat: 'markdown' }
      };

      const result = await contentProcessor.processEnhancedScrape(request);

      expect(result.success).toBe(true);
      expect(result.content).toContain('Pricing Plans');
      expect(result.content).toContain('FAQ');
    });
  });

  describe('Database Integration', () => {
    it('should store scraped data in database when persistent storage enabled', async () => {
      mockCostOptimizer.scrapeWithMaxCostOptimization.mockResolvedValue({
        success: true,
        content: 'Test content',
        url: 'https://example.com',
        metadata: { creditsUsed: 1 }
      });

      mockMediaExtractor.collectImagesWithPriority.mockResolvedValue({
        favicon: ['https://example.com/favicon.ico'],
        ogImages: [],
        screenshot: null
      });

      mockMultiPageScraper.discoverAndPlanScraping.mockResolvedValue({
        scrapeNow: [],
        queueForLater: [],
        skipPages: [],
        reason: 'No additional pages'
      });

      mockSupabase.insert.mockResolvedValue({
        data: { id: 1 },
        error: null
      });

      const request: EnhancedScrapeRequest = {
        url: 'https://example.com',
        options: { outputFormat: 'markdown' },
        persistentStorage: true
      };

      const result = await contentProcessor.processEnhancedScrape(request);

      expect(result.success).toBe(true);
      // Database storage should be attempted
      expect(mockSupabase.from).toHaveBeenCalled();
    });

    it('should handle database storage failures gracefully', async () => {
      mockCostOptimizer.scrapeWithMaxCostOptimization.mockResolvedValue({
        success: true,
        content: 'Test content',
        url: 'https://example.com',
        metadata: { creditsUsed: 1 }
      });

      mockMediaExtractor.collectImagesWithPriority.mockResolvedValue({
        favicon: null,
        ogImages: [],
        screenshot: null
      });

      mockMultiPageScraper.discoverAndPlanScraping.mockResolvedValue({
        scrapeNow: [],
        queueForLater: [],
        skipPages: [],
        reason: 'No additional pages'
      });

      mockSupabase.insert.mockResolvedValue({
        data: null,
        error: { message: 'Database error' }
      });

      const request: EnhancedScrapeRequest = {
        url: 'https://example.com',
        options: { outputFormat: 'markdown' },
        persistentStorage: true
      };

      const result = await contentProcessor.processEnhancedScrape(request);

      // Should still succeed even if database storage fails
      expect(result.success).toBe(true);
      expect(result.content).toBe('Test content');
    });
  });

  describe('File System Storage', () => {
    it('should store scraped content to file system when enabled', async () => {
      mockCostOptimizer.scrapeWithMaxCostOptimization.mockResolvedValue({
        success: true,
        content: 'Test content for file storage',
        url: 'https://example.com',
        metadata: { creditsUsed: 1 }
      });

      mockMediaExtractor.collectImagesWithPriority.mockResolvedValue({
        favicon: null,
        ogImages: [],
        screenshot: null
      });

      mockMultiPageScraper.discoverAndPlanScraping.mockResolvedValue({
        scrapeNow: [],
        queueForLater: [],
        skipPages: [],
        reason: 'No additional pages'
      });

      mockFs.mkdir.mockResolvedValue(undefined);
      mockFs.writeFile.mockResolvedValue(undefined);

      const request: EnhancedScrapeRequest = {
        url: 'https://example.com',
        options: { outputFormat: 'markdown' },
        persistentStorage: true
      };

      const result = await contentProcessor.processEnhancedScrape(request);

      expect(result.success).toBe(true);
      // File system operations should be attempted
      expect(mockFs.writeFile).toHaveBeenCalled();
    });

    it('should handle file system errors gracefully', async () => {
      mockCostOptimizer.scrapeWithMaxCostOptimization.mockResolvedValue({
        success: true,
        content: 'Test content',
        url: 'https://example.com',
        metadata: { creditsUsed: 1 }
      });

      mockMediaExtractor.collectImagesWithPriority.mockResolvedValue({
        favicon: null,
        ogImages: [],
        screenshot: null
      });

      mockMultiPageScraper.discoverAndPlanScraping.mockResolvedValue({
        scrapeNow: [],
        queueForLater: [],
        skipPages: [],
        reason: 'No additional pages'
      });

      mockFs.writeFile.mockRejectedValue(new Error('File system error'));

      const request: EnhancedScrapeRequest = {
        url: 'https://example.com',
        options: { outputFormat: 'markdown' },
        persistentStorage: true
      };

      const result = await contentProcessor.processEnhancedScrape(request);

      // Should still succeed even if file storage fails
      expect(result.success).toBe(true);
      expect(result.content).toBe('Test content');
    });
  });

  describe('Performance and Monitoring', () => {
    it('should track performance metrics throughout workflow', async () => {
      const startTime = Date.now();

      mockCostOptimizer.scrapeWithMaxCostOptimization.mockImplementation(async () => {
        await new Promise(resolve => setTimeout(resolve, 100)); // Simulate delay
        return {
          success: true,
          content: 'Test content',
          url: 'https://example.com',
          metadata: { creditsUsed: 1 }
        };
      });

      mockMediaExtractor.collectImagesWithPriority.mockResolvedValue({
        favicon: null,
        ogImages: [],
        screenshot: null
      });

      mockMultiPageScraper.discoverAndPlanScraping.mockResolvedValue({
        scrapeNow: [],
        queueForLater: [],
        skipPages: [],
        reason: 'No additional pages'
      });

      const request: EnhancedScrapeRequest = {
        url: 'https://example.com',
        options: { outputFormat: 'markdown' }
      };

      const result = await contentProcessor.processEnhancedScrape(request);

      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(result.success).toBe(true);
      expect(duration).toBeGreaterThan(100); // Should include processing time
    });

    it('should handle timeout scenarios', async () => {
      mockCostOptimizer.scrapeWithMaxCostOptimization.mockImplementation(async () => {
        await new Promise(resolve => setTimeout(resolve, 5000)); // Long delay
        return {
          success: true,
          content: 'Delayed content',
          url: 'https://example.com',
          metadata: { creditsUsed: 1 }
        };
      });

      const request: EnhancedScrapeRequest = {
        url: 'https://example.com',
        options: { 
          outputFormat: 'markdown',
          timeout: 1000 // Short timeout
        }
      };

      const result = await contentProcessor.processEnhancedScrape(request);

      // Should handle timeout appropriately
      expect(result.success).toBe(false);
      expect(result.error).toContain('timeout');
    });
  });
});
